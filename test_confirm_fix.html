<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Confirm 修复测试</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            max-width: 800px;
            margin: 50px auto;
            padding: 20px;
        }
        .test-section {
            margin: 20px 0;
            padding: 15px;
            border: 1px solid #ddd;
            border-radius: 5px;
        }
        button {
            margin: 5px;
            padding: 10px 15px;
            background: #007bff;
            color: white;
            border: none;
            border-radius: 3px;
            cursor: pointer;
        }
        button:hover {
            background: #0056b3;
        }
        .log {
            background: #f8f9fa;
            border: 1px solid #dee2e6;
            padding: 10px;
            margin: 10px 0;
            border-radius: 3px;
            font-family: monospace;
            white-space: pre-wrap;
            max-height: 200px;
            overflow-y: auto;
        }
    </style>
</head>
<body>
    <h1>Confirm 函数修复测试</h1>
    
    <div class="test-section">
        <h3>测试 1: data-onclick 属性中的 confirm 调用</h3>
        <button data-onclick="confirm('这是一个测试确认消息')">测试 confirm('message')</button>
        <button data-onclick='confirm("双引号消息测试")'>测试 confirm("message")</button>
        <button data-onclick="confirm('确定要删除这个项目吗？')">测试删除确认</button>
    </div>
    
    <div class="test-section">
        <h3>测试 2: 复杂的 confirm 调用</h3>
        <button data-onclick="return confirm('确定要继续吗？')">测试 return confirm</button>
        <button data-onclick="if(confirm('确定要执行此操作吗？')) { alert('已确认'); }">测试条件 confirm</button>
    </div>
    
    <div class="test-section">
        <h3>测试 3: 事件处理器管理器</h3>
        <button data-validation="critical" data-message="这是关键操作确认">关键操作测试</button>
    </div>
    
    <div class="test-section">
        <h3>测试 4: 安全删除操作</h3>
        <button data-action="safe-delete" data-delete-code="alert('删除操作执行')" data-confirm-message="确定要删除吗？">安全删除测试</button>
    </div>
    
    <div class="test-section">
        <h3>控制台日志</h3>
        <div id="logOutput" class="log">控制台输出将显示在这里...</div>
        <button onclick="clearLog()">清空日志</button>
    </div>

    <!-- 加载事件处理器 -->
    <script src="app/static/js/event-handler-manager.js"></script>
    <script src="app/static/js/universal-event-handler.js"></script>
    <script src="app/static/js/comprehensive-event-handler.js"></script>
    <script src="app/static/js/critical-handler-simple.js"></script>
    
    <script>
        // 捕获控制台输出并显示在页面上
        const logOutput = document.getElementById('logOutput');
        const originalLog = console.log;
        const originalWarn = console.warn;
        const originalError = console.error;
        
        function addToLog(type, ...args) {
            const timestamp = new Date().toLocaleTimeString();
            const message = `[${timestamp}] ${type}: ${args.join(' ')}\n`;
            logOutput.textContent += message;
            logOutput.scrollTop = logOutput.scrollHeight;
        }
        
        console.log = function(...args) {
            originalLog.apply(console, args);
            addToLog('LOG', ...args);
        };
        
        console.warn = function(...args) {
            originalWarn.apply(console, args);
            addToLog('WARN', ...args);
        };
        
        console.error = function(...args) {
            originalError.apply(console, args);
            addToLog('ERROR', ...args);
        };
        
        function clearLog() {
            logOutput.textContent = '控制台输出将显示在这里...\n';
        }
        
        // 测试函数
        function testConfirm() {
            console.log('开始测试 confirm 修复...');
            
            // 模拟各种 confirm 调用
            const testCases = [
                "confirm('测试消息1')",
                'confirm("测试消息2")',
                "confirm('确定要删除吗？')",
                "return confirm('返回测试')"
            ];
            
            testCases.forEach((testCase, index) => {
                console.log(`测试用例 ${index + 1}: ${testCase}`);
                try {
                    if (window.EventHandlerManager && window.EventHandlerManager.safeExecute) {
                        const result = window.EventHandlerManager.safeExecute(testCase);
                        console.log(`结果: ${result}`);
                    } else {
                        console.warn('EventHandlerManager 未加载');
                    }
                } catch (error) {
                    console.error(`测试失败: ${error.message}`);
                }
            });
        }
        
        // 页面加载完成后运行测试
        document.addEventListener('DOMContentLoaded', function() {
            console.log('页面加载完成，开始测试...');
            setTimeout(testConfirm, 1000); // 延迟1秒确保所有脚本都加载完成
        });
    </script>
</body>
</html>
