<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>智慧食堂平台</title>
    <style>
        /* 基础样式 */
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            font-family: system-ui, -apple-system, sans-serif;
            line-height: 1.6;
            color: #333;
            background: #f8f9fa;
        }

        .container {
            max-width: 1200px;
            margin: 0 auto;
            padding: 0 20px;
        }

        /* 导航栏 */
        header {
            background: linear-gradient(135deg, #165DFF, #0D47A1);
            color: white;
            padding: 1rem 0;
            box-shadow: 0 2px 4px rgba(0,0,0,0.1);
            position: fixed;
            width: 100%;
            top: 0;
            z-index: 1000;
        }

        nav {
            display: flex;
            justify-content: space-between;
            align-items: center;
        }

        nav a {
            color: white;
            text-decoration: none;
            margin-left: 20px;
            font-weight: 500;
            transition: opacity 0.3s;
        }

        nav a:hover {
            opacity: 0.8;
        }

        /* 主横幅 */
        .hero {
            text-align: center;
            padding: 160px 0 80px;
            background: linear-gradient(135deg, #f8f9fa 0%, #e9ecef 100%);
        }

        .hero h1 {
            font-size: 3rem;
            margin-bottom: 1.5rem;
            color: #1a1a1a;
        }

        .hero p {
            font-size: 1.25rem;
            color: #666;
            max-width: 600px;
            margin: 0 auto 2rem;
        }

        /* 按钮样式 */
        .btn {
            display: inline-block;
            padding: 12px 24px;
            background: #165DFF;
            color: white;
            text-decoration: none;
            border-radius: 6px;
            margin: 10px;
            font-weight: 500;
            transition: all 0.3s;
        }

        .btn:hover {
            transform: translateY(-2px);
            box-shadow: 0 4px 12px rgba(22, 93, 255, 0.2);
        }

        .btn-outline {
            background: transparent;
            border: 2px solid #165DFF;
            color: #165DFF;
        }

        .btn-outline:hover {
            background: #165DFF;
            color: white;
        }

        /* 功能特点 */
        .features {
            padding: 80px 0;
            background: white;
        }

        .features h2 {
            text-align: center;
            font-size: 2.5rem;
            margin-bottom: 3rem;
            color: #1a1a1a;
        }

        .feature-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
            gap: 30px;
            margin-top: 30px;
        }

        .feature-card {
            padding: 30px;
            background: white;
            border-radius: 12px;
            box-shadow: 0 4px 6px rgba(0,0,0,0.05);
            transition: all 0.3s;
        }

        .feature-card:hover {
            transform: translateY(-5px);
            box-shadow: 0 8px 15px rgba(0,0,0,0.1);
        }

        .feature-card h3 {
            font-size: 1.5rem;
            margin-bottom: 1rem;
            color: #1a1a1a;
        }

        .feature-card p {
            color: #666;
            line-height: 1.6;
        }

        /* 联系我们 */
        .contact {
            background: #f8f9fa;
            padding: 80px 0;
        }

        .contact h2 {
            text-align: center;
            font-size: 2.5rem;
            margin-bottom: 3rem;
            color: #1a1a1a;
        }

        .contact-content {
            max-width: 600px;
            margin: 0 auto;
            background: white;
            padding: 40px;
            border-radius: 12px;
            box-shadow: 0 4px 6px rgba(0,0,0,0.05);
        }

        .contact-info {
            margin-bottom: 30px;
        }

        .contact-info p {
            margin: 10px 0;
            color: #666;
            font-size: 1.1rem;
        }

        .contact-form input,
        .contact-form textarea {
            width: 100%;
            padding: 12px;
            margin-bottom: 15px;
            border: 1px solid #ddd;
            border-radius: 6px;
            font-size: 1rem;
            transition: border-color 0.3s;
        }

        .contact-form input:focus,
        .contact-form textarea:focus {
            outline: none;
            border-color: #165DFF;
        }

        .contact-form textarea {
            height: 150px;
            resize: vertical;
        }

        /* 页脚 */
        footer {
            background: #1a1a1a;
            color: white;
            padding: 40px 0;
            text-align: center;
        }

        footer p {
            margin: 5px 0;
            color: #999;
        }

        /* 响应式设计 */
        @media (max-width: 768px) {
            .hero h1 {
                font-size: 2rem;
            }

            .hero p {
                font-size: 1.1rem;
            }

            .features h2,
            .contact h2 {
                font-size: 2rem;
            }

            .feature-grid {
                grid-template-columns: 1fr;
            }

            .contact-content {
                padding: 20px;
            }
        }
    </style>
</head>
<body>
    <header>
        <div class="container">
            <nav>
                <a href="/" style="font-size: 1.5rem; font-weight: bold;">智慧食堂平台</a>
                <div>
                    <a href="#features">功能特点</a>
                    <a href="#contact">联系我们</a>
                    <a href="{{ url_for('auth.login') }}" class="btn">登录</a>
                </div>
            </nav>
        </div>
    </header>

    <section class="hero">
        <div class="container">
            <h1>智慧食堂管理平台</h1>
            <p>打造智能化食堂管理，提升食品安全与效率，为校园食堂提供全方位的数字化解决方案</p>
            <div>
                <a href="{{ url_for('auth.register') }}" class="btn">免费注册</a>
                <a href="#features" class="btn btn-outline">了解更多</a>
            </div>
        </div>
    </section>

    <section id="features" class="features">
        <div class="container">
            <h2>核心功能</h2>
            <div class="feature-grid">
                <div class="feature-card">
                    <h3>食品安全管理</h3>
                    <p>全程追溯，确保食品安全。从食材采购到餐后服务，全方位保障食品安全。</p>
                </div>
                <div class="feature-card">
                    <h3>数据分析</h3>
                    <p>智能分析，优化运营效率。通过数据驱动决策，提升食堂管理水平。</p>
                </div>
                <div class="feature-card">
                    <h3>家校互动</h3>
                    <p>实时沟通，提升服务质量。建立家校互动平台，提供更好的服务体验。</p>
                </div>
            </div>
        </div>
    </section>

    <!-- 视频教程区域 -->
    <section id="tutorials" class="tutorials">
        <div class="container">
            <h2>使用教程</h2>
            <p class="section-desc">观看视频教程，快速掌握系统使用方法</p>
            
            <div class="tutorial-grid">
                <!-- 视频卡片 -->
                <div class="tutorial-card" data-video-id="1">
                    <div class="tutorial-thumbnail">
                        <img src="{{ url_for('static', filename='images/tutorials/tutorial-1.jpg') }}" alt="系统概览">
                        <div class="play-button">
                            <i class="fas fa-play"></i>
                        </div>
                    </div>
                    <div class="tutorial-info">
                        <h3>系统概览</h3>
                        <p>了解系统整体架构和主要功能模块</p>
                        <span class="duration">05:30</span>
                    </div>
                </div>

                <div class="tutorial-card" data-video-id="2">
                    <div class="tutorial-thumbnail">
                        <img src="{{ url_for('static', filename='images/tutorials/tutorial-2.jpg') }}" alt="基础操作">
                        <div class="play-button">
                            <i class="fas fa-play"></i>
                        </div>
                    </div>
                    <div class="tutorial-info">
                        <h3>基础操作指南</h3>
                        <p>掌握系统的基本操作和常用功能</p>
                        <span class="duration">08:15</span>
                    </div>
                </div>

                <div class="tutorial-card" data-video-id="3">
                    <div class="tutorial-thumbnail">
                        <img src="{{ url_for('static', filename='images/tutorials/tutorial-3.jpg') }}" alt="高级功能">
                        <div class="play-button">
                            <i class="fas fa-play"></i>
                        </div>
                    </div>
                    <div class="tutorial-info">
                        <h3>高级功能详解</h3>
                        <p>深入理解系统的核心功能和特色</p>
                        <span class="duration">12:45</span>
                    </div>
                </div>
            </div>

            <!-- 视频播放模态框 -->
            <div id="videoModal" class="modal">
                <div class="modal-content">
                    <span class="close-button">&times;</span>
                    <div class="video-container">
                        <video id="tutorialVideo" controls>
                            <source src="" type="video/mp4">
                            您的浏览器不支持视频播放。
                        </video>
                    </div>
                    <div class="video-info">
                        <h3 id="videoTitle"></h3>
                        <p id="videoDescription"></p>
                    </div>
                </div>
            </div>
        </div>
    </section>

    <section id="contact" class="contact">
        <div class="container">
            <h2>联系我们</h2>
            <div class="contact-content">
                <div class="contact-info">
                    <p>📞 电话：18373062333</p>
                    <p>📧 邮箱：<EMAIL></p>
                    <p>📍 地址：湖南.岳阳</p>
                </div>
                <form class="contact-form" id="consultationForm" method="POST" action="{{ url_for('consultation.submit_consultation') }}">
                    <input type="hidden" name="csrf_token" value="{{ csrf_token() }}">
                    <div class="form-group">
                        <input type="text" name="name" placeholder="您的姓名" required minlength="2" maxlength="50">
                    </div>
                    <div class="form-group">
                        <select name="contact_type" required>
                            <option value="微信">微信</option>
                            <option value="电话">电话</option>
                            <option value="邮箱">邮箱</option>
                        </select>
                    </div>
                    <div class="form-group">
                        <input type="text" name="contact_value" placeholder="您的联系方式" required minlength="3" maxlength="100">
                    </div>
                    <div class="form-group">
                        <textarea name="content" placeholder="请输入您的需求" required minlength="10" maxlength="1000"></textarea>
                    </div>
                    <button type="submit" class="btn">提交咨询</button>
                </form>
            </div>
        </div>
    </section>

    <footer>
        <div class="container">
            <p>© 2024 智慧食堂平台</p>
            <p>专注校园食堂智能化管理解决方案</p>
        </div>
    </footer>
</body>
</html>