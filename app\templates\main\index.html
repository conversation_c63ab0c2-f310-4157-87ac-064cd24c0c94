<!DOCTYPE html>
<html lang="zh-CN">
<head>
  <meta charset="UTF-8">
  <meta name="viewport" content="width=device-width, initial-scale=1.0">
  <title>校园餐智慧食堂平台 - 智能化食堂管理解决方案</title>

  <!-- 本地化CSS资源 -->
  <link href="{{ url_for('static', filename='css/homepage.css') }}?v=2.0" rel="stylesheet">
  <link href="{{ url_for('static', filename='css/icons.css') }}?v=2.0" rel="stylesheet">

  <!-- jQuery (必须在其他脚本之前加载) -->
  <script nonce="{{ csp_nonce }}" src="{{ url_for('static', filename='vendor/jquery/jquery.min.js') }}?v=1.0.0"></script>

  <!-- 事件处理器管理器 -->
  <script nonce="{{ csp_nonce }}" src="{{ url_for('static', filename='js/event-handler-manager.js') }}"></script>

  <!-- 本地化JavaScript资源 -->
  <script nonce="{{ csp_nonce }}" src="{{ url_for('static', filename='js/simple-charts.js') }}?v=2.0"></script>
  <script nonce="{{ csp_nonce }}" src="{{ url_for('static', filename='js/simple-carousel.js') }}?v=2.0"></script>

  <!-- 前端错误监控脚本 -->
  <script nonce="{{ csp_nonce }}" src="{{ url_for('static', filename='js/frontend-error-monitor.js') }}"></script>




</head>

<body class="bg-dark-blue text-white">
  <!-- 背景网格 -->
  <div class="fixed inset-0 bg-grid" style="z-index: 0; opacity: 0.5;"></div>

  <!-- 导航栏 -->
  <header id="navbar" class="navbar">
    <div class="container">
      <div class="flex justify-between items-center h-16">
        <div class="flex items-center">
          <a href="#" class="navbar-brand">
            <div class="w-10 h-10 bg-primary rounded-lg flex items-center justify-center neon-glow">
              <i class="fa fa-cutlery text-white text-xl"></i>
            </div>
            <span class="ml-2">智慧食堂<span class="text-neon-blue neon-text">平台</span></span>
          </a>
        </div>

        <!-- 桌面导航 -->
        <nav class="hidden md:flex space-x-8">
          <a href="#features" class="nav-link">核心功能</a>
          <a href="#contact" class="nav-link">联系我们</a>
        </nav>

        <!-- 移动端菜单按钮 -->
        <div class="md:hidden">
          <button id="menu-toggle" class="text-white transition-colors">
            <i class="fa fa-bars text-2xl"></i>
          </button>
        </div>
      </div>
    </div>

    <!-- 移动端导航菜单 -->
    <div id="mobile-menu" class="md:hidden hidden" style="background: rgba(11, 14, 47, 0.95); backdrop-filter: blur(12px); border-top: 1px solid rgba(22, 93, 255, 0.2);">
      <div class="container py-3 space-y-3">
        <a href="#features" class="nav-link block py-2">核心功能</a>
        <a href="#contact" class="nav-link block py-2">联系我们</a>
      </div>
    </div>
  </header>

  <!-- 英雄区域 -->
  <section class="pt-32 pb-24 bg-gradient-to-b relative" style="overflow: hidden;">
    <!-- 背景装饰 -->
    <div class="absolute" style="top: -10rem; left: -10rem; width: 20rem; height: 20rem; background: rgba(0, 191, 255, 0.2); border-radius: 50%; filter: blur(3rem);"></div>
    <div class="absolute" style="bottom: -10rem; right: -10rem; width: 20rem; height: 20rem; background: rgba(157, 78, 221, 0.2); border-radius: 50%; filter: blur(3rem);"></div>

    <div class="container relative" style="z-index: 10;">
      <div class="flex flex-col lg:flex-row items-center">
        <div class="lg:w-1/2 mb-10">
          <h1 class="text-4xl font-bold leading-tight text-white mb-6">
            智慧食堂管理平台<br>
            <span class="text-neon-blue neon-text">全方位智能化解决方案</span>
          </h1>
          <p class="text-lg text-gray-300 mb-8">
            致力于打造全方位智能化管理体系，实现食品安全可视化、可管控、可追溯，为校园食堂管理提供高效便捷的技术支持。
          </p>
          <div class="flex flex-wrap gap-4">
            <a href="{{ url_for('auth.login') }}" class="btn btn-primary">
              立即登录
              <i class="fa fa-sign-in ml-2"></i>
            </a>
            <a href="{{ url_for('auth.register') }}" class="btn btn-outline">
              免费注册
              <i class="fa fa-user-plus ml-2"></i>
            </a>
            <a href="#features" class="btn btn-outline text-sm">
              了解功能
              <i class="fa fa-arrow-down ml-2"></i>
            </a>
          </div>

          <!-- 数据指标区域 -->
          <div class="mt-12">
            <div class="grid grid-cols-3 gap-4">
              <div class="backdrop-blur-sm p-4 rounded-lg border border-primary/20" style="background: rgba(29, 33, 41, 0.3);">
                <p class="text-neon-blue text-2xl font-bold">99.9%</p>
                <p class="text-sm text-gray-400">系统稳定性</p>
              </div>
              <div class="backdrop-blur-sm p-4 rounded-lg border border-primary/20" style="background: rgba(29, 33, 41, 0.3);">
                <p class="text-neon-green text-2xl font-bold">80%</p>
                <p class="text-sm text-gray-400">管理效率提升</p>
              </div>
              <div class="backdrop-blur-sm p-4 rounded-lg border border-primary/20" style="background: rgba(29, 33, 41, 0.3);">
                <p class="text-neon-purple text-2xl font-bold">100%</p>
                <p class="text-sm text-gray-400">食品溯源率</p>
              </div>
            </div>
          </div>
        </div>

        <!-- 轮播图区域 -->
        <div class="lg:w-1/2 relative">
          <div class="relative animate-float">
            <!-- 轮播图容器 -->
            <div class="backdrop-blur-md rounded-2xl shadow-2xl border border-primary/30" style="background: rgba(29, 33, 41, 0.5);">
              <div id="heroCarousel">
                <!-- 轮播图内容将由JavaScript动态生成 -->
              </div>
            </div>
          </div>
        </div>

      </div>
    </div>
  </section>



  <!-- 核心功能 -->
  <section id="features" class="section-padding bg-dark-blue relative overflow-hidden">
    <!-- 背景装饰 -->
    <div class="absolute top-1/4 -left-20 w-60 h-60 bg-neon-purple/10 rounded-full blur-3xl"></div>
    <div class="absolute bottom-1/4 -right-20 w-60 h-60 bg-neon-blue/10 rounded-full blur-3xl"></div>

    <div class="container mx-auto px-4 sm:px-6 lg:px-8 relative z-10">
      <div class="text-center max-w-3xl mx-auto mb-16">
        <h2 class="text-[clamp(1.8rem,4vw,2.8rem)] font-bold text-white mb-4">八大智能化功能，全面保障食堂安全</h2>
        <p class="text-lg text-gray-300">
          我们的智慧食堂平台集成了多项先进功能，从食材采购到餐后服务，全方位提升食堂管理效率与食品安全
        </p>
      </div>

      <!-- 功能分组导航 -->
      <div class="flex justify-center mb-12 space-x-4 overflow-x-auto pb-4">
        <button class="px-6 py-2 rounded-full bg-primary/20 text-white hover:bg-primary/30 transition-colors whitespace-nowrap">
          食品安全管理
        </button>
        <button class="px-6 py-2 rounded-full bg-primary/10 text-gray-400 hover:bg-primary/20 transition-colors whitespace-nowrap">
          运营效率提升
        </button>
        <button class="px-6 py-2 rounded-full bg-primary/10 text-gray-400 hover:bg-primary/20 transition-colors whitespace-nowrap">
          家校互动管理
        </button>
      </div>

      <!-- 功能展示区域 -->
      <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-8">
        <!-- 食品安全管理组 -->
        <div class="group">
          <div class="bg-dark/30 backdrop-blur-sm rounded-xl shadow-md p-6 card-hover border border-primary/20 h-full">
            <div class="flex items-center justify-between mb-4">
              <div class="w-14 h-14 bg-primary/20 rounded-lg flex items-center justify-center group-hover:bg-primary/30 transition-colors">
            <i class="fa fa-search text-neon-blue text-2xl"></i>
              </div>
              <span class="text-xs px-3 py-1 rounded-full bg-neon-blue/20 text-neon-blue">食品安全</span>
          </div>
          <h3 class="text-xl font-bold mb-3 text-white">智能检查系统</h3>
            <p class="text-gray-400 mb-4">
            员工通过扫码上传食堂卫生状况、设备运行情况，管理员在线进行评价反馈，实时监控食堂运营状态
          </p>
            <div class="flex items-center text-neon-blue text-sm">
              <span>查看详情</span>
              <i class="fa fa-arrow-right ml-2 group-hover:translate-x-1 transition-transform"></i>
            </div>
          </div>
        </div>

        <div class="group">
          <div class="bg-dark/30 backdrop-blur-sm rounded-xl shadow-md p-6 card-hover border border-primary/20 h-full">
            <div class="flex items-center justify-between mb-4">
              <div class="w-14 h-14 bg-primary/20 rounded-lg flex items-center justify-center group-hover:bg-primary/30 transition-colors">
                <i class="fa fa-qrcode text-neon-blue text-2xl"></i>
              </div>
              <span class="text-xs px-3 py-1 rounded-full bg-neon-blue/20 text-neon-blue">食品安全</span>
            </div>
            <h3 class="text-xl font-bold mb-3 text-white">全程溯源</h3>
            <p class="text-gray-400 mb-4">
              实现食品从源头到餐桌的全程可追溯，打造透明化供应链，明确安全责任到人
            </p>
            <div class="flex items-center text-neon-blue text-sm">
              <span>查看详情</span>
              <i class="fa fa-arrow-right ml-2 group-hover:translate-x-1 transition-transform"></i>
            </div>
          </div>
        </div>

        <div class="group">
          <div class="bg-dark/30 backdrop-blur-sm rounded-xl shadow-md p-6 card-hover border border-primary/20 h-full">
            <div class="flex items-center justify-between mb-4">
              <div class="w-14 h-14 bg-primary/20 rounded-lg flex items-center justify-center group-hover:bg-primary/30 transition-colors">
                <i class="fa fa-print text-neon-blue text-2xl"></i>
              </div>
              <span class="text-xs px-3 py-1 rounded-full bg-neon-blue/20 text-neon-blue">食品安全</span>
            </div>
            <h3 class="text-xl font-bold mb-3 text-white">一键式留样标签</h3>
            <p class="text-gray-400 mb-4">
              规范食品留样流程，自动生成留样标签，确保食品安全管理符合规范要求
            </p>
            <div class="flex items-center text-neon-blue text-sm">
              <span>查看详情</span>
              <i class="fa fa-arrow-right ml-2 group-hover:translate-x-1 transition-transform"></i>
            </div>
          </div>
        </div>

        <!-- 运营效率提升组 -->
        <div class="group">
          <div class="bg-dark/30 backdrop-blur-sm rounded-xl shadow-md p-6 card-hover border border-primary/20 h-full">
            <div class="flex items-center justify-between mb-4">
              <div class="w-14 h-14 bg-primary/20 rounded-lg flex items-center justify-center group-hover:bg-primary/30 transition-colors">
                <i class="fa fa-shopping-cart text-neon-blue text-2xl"></i>
              </div>
              <span class="text-xs px-3 py-1 rounded-full bg-neon-green/20 text-neon-green">运营效率</span>
            </div>
            <h3 class="text-xl font-bold mb-3 text-white">智能采购系统</h3>
            <p class="text-gray-400 mb-4">
              提供供应商灵活选择功能，支持智能价格对比，依据采购需求自动生成采购单，简化采购流程
            </p>
            <div class="flex items-center text-neon-blue text-sm">
              <span>查看详情</span>
              <i class="fa fa-arrow-right ml-2 group-hover:translate-x-1 transition-transform"></i>
            </div>
          </div>
        </div>

        <div class="group">
          <div class="bg-dark/30 backdrop-blur-sm rounded-xl shadow-md p-6 card-hover border border-primary/20 h-full">
            <div class="flex items-center justify-between mb-4">
              <div class="w-14 h-14 bg-primary/20 rounded-lg flex items-center justify-center group-hover:bg-primary/30 transition-colors">
                <i class="fa fa-exchange text-neon-blue text-2xl"></i>
              </div>
              <span class="text-xs px-3 py-1 rounded-full bg-neon-green/20 text-neon-green">运营效率</span>
            </div>
            <h3 class="text-xl font-bold mb-3 text-white">出入库管理</h3>
            <p class="text-gray-400 mb-4">
              对出入库流程进行完整管理，自动生成台账报表，实时监控库存情况，确保库存管理精准高效
            </p>
            <div class="flex items-center text-neon-blue text-sm">
              <span>查看详情</span>
              <i class="fa fa-arrow-right ml-2 group-hover:translate-x-1 transition-transform"></i>
            </div>
          </div>
        </div>

        <div class="group">
          <div class="bg-dark/30 backdrop-blur-sm rounded-xl shadow-md p-6 card-hover border border-primary/20 h-full">
            <div class="flex items-center justify-between mb-4">
              <div class="w-14 h-14 bg-primary/20 rounded-lg flex items-center justify-center group-hover:bg-primary/30 transition-colors">
                <i class="fa fa-list text-neon-blue text-2xl"></i>
              </div>
              <span class="text-xs px-3 py-1 rounded-full bg-neon-green/20 text-neon-green">运营效率</span>
            </div>
            <h3 class="text-xl font-bold mb-3 text-white">灵活菜单管理</h3>
            <p class="text-gray-400 mb-4">
              支持周菜单灵活安排与调整，可直接打印输出，一键导入菜单信息生成带价格及营养分析的带量食谱
            </p>
            <div class="flex items-center text-neon-blue text-sm">
              <span>查看详情</span>
              <i class="fa fa-arrow-right ml-2 group-hover:translate-x-1 transition-transform"></i>
            </div>
          </div>
        </div>

        <!-- 家校互动管理组 -->
        <div class="group">
          <div class="bg-dark/30 backdrop-blur-sm rounded-xl shadow-md p-6 card-hover border border-primary/20 h-full">
            <div class="flex items-center justify-between mb-4">
              <div class="w-14 h-14 bg-primary/20 rounded-lg flex items-center justify-center group-hover:bg-primary/30 transition-colors">
                <i class="fa fa-users text-neon-blue text-2xl"></i>
              </div>
              <span class="text-xs px-3 py-1 rounded-full bg-neon-purple/20 text-neon-purple">家校互动</span>
          </div>
          <h3 class="text-xl font-bold mb-3 text-white">家校共陪餐</h3>
            <p class="text-gray-400 mb-4">
            邀请家长参与陪餐体验，提升食堂管理透明度，加强家校互动沟通，增强家长对食堂的信任度
          </p>
            <div class="flex items-center text-neon-blue text-sm">
              <span>查看详情</span>
              <i class="fa fa-arrow-right ml-2 group-hover:translate-x-1 transition-transform"></i>
            </div>
          </div>
        </div>

        <div class="group">
          <div class="bg-dark/30 backdrop-blur-sm rounded-xl shadow-md p-6 card-hover border border-primary/20 h-full">
            <div class="flex items-center justify-between mb-4">
              <div class="w-14 h-14 bg-primary/20 rounded-lg flex items-center justify-center group-hover:bg-primary/30 transition-colors">
            <i class="fa fa-file-text-o text-neon-blue text-2xl"></i>
              </div>
              <span class="text-xs px-3 py-1 rounded-full bg-neon-purple/20 text-neon-purple">家校互动</span>
            </div>
            <h3 class="text-xl font-bold mb-3 text-white">智能日志生成</h3>
            <p class="text-gray-400 mb-4">
              每日自动生成食堂工作日志，完整记录运营情况，基于数据进行智能分析，为管理决策提供依据
            </p>
            <div class="flex items-center text-neon-blue text-sm">
              <span>查看详情</span>
              <i class="fa fa-arrow-right ml-2 group-hover:translate-x-1 transition-transform"></i>
            </div>
          </div>
        </div>
      </div>

      <!-- 功能演示视频 -->
      <div class="mt-16 bg-dark/30 backdrop-blur-sm rounded-xl p-8 border border-primary/20">
        <div class="text-center mb-8">
          <h3 class="text-2xl font-bold text-white mb-2">功能演示</h3>
          <p class="text-gray-400">观看视频了解系统功能详情</p>
        </div>
        <div class="aspect-w-16 aspect-h-9 rounded-lg overflow-hidden">
          <div class="w-full h-96 bg-dark/50 rounded-lg flex items-center justify-center">
            <div class="text-center">
              <i class="fa fa-play-circle text-6xl text-neon-blue mb-4"></i>
              <p class="text-white">点击播放功能演示视频</p>
            </div>
          </div>
        </div>
      </div>
    </div>
  </section>





  <!-- 联系我们 -->
  <section id="contact" class="section-padding bg-dark/50 relative overflow-hidden">
    <div class="container mx-auto px-4 sm:px-6 lg:px-8">
      <div class="text-center max-w-3xl mx-auto mb-16">
        <h2 class="text-[clamp(1.8rem,4vw,2.8rem)] font-bold text-white mb-4">联系我们</h2>
        <p class="text-lg text-gray-300">
          如果您有任何问题或需求，请随时与我们联系
        </p>
      </div>

      <div class="grid grid-cols-1 md:grid-cols-2 gap-8">
        <!-- 联系信息 -->
        <div class="bg-dark/30 backdrop-blur-sm rounded-xl p-8 border border-primary/20">
          <h3 class="text-xl font-bold text-white mb-6">联系方式</h3>

          <!-- 联系方式布局 -->
          <div class="flex flex-col lg:flex-row gap-8">
            <!-- 左侧：联系方式列表 -->
            <div class="flex-1">
              <div class="space-y-4">
                <div class="flex items-center space-x-4">
                  <div class="w-12 h-12 bg-primary/20 rounded-lg flex items-center justify-center">
                    <i class="fa fa-phone text-neon-blue text-xl"></i>
                  </div>
                  <div>
                    <p class="text-gray-400">电话咨询</p>
                    <p class="text-white font-medium">18373062333</p>
                  </div>
                </div>
                <div class="flex items-center space-x-4">
                  <div class="w-12 h-12 bg-primary/20 rounded-lg flex items-center justify-center">
                    <i class="fa fa-envelope text-neon-blue text-xl"></i>
                  </div>
                  <div>
                    <p class="text-gray-400">邮件咨询</p>
                    <p class="text-white font-medium"><EMAIL></p>
                  </div>
                </div>
                <div class="flex items-center space-x-4">
                  <div class="w-12 h-12 bg-primary/20 rounded-lg flex items-center justify-center">
                    <i class="fa fa-map-marker text-neon-blue text-xl"></i>
                  </div>
                  <div>
                    <p class="text-gray-400">联系地址</p>
                    <p class="text-white font-medium">湖南.岳阳</p>
                  </div>
                </div>
              </div>
            </div>

            <!-- 右侧：微信二维码 -->
            <div class="flex-shrink-0">
              <div class="text-center">
                <h4 class="text-lg font-bold text-white mb-4">
                  <i class="fa fa-weixin text-neon-green mr-2"></i>
                  微信咨询
                </h4>
                <div class="bg-white/10 backdrop-blur-sm rounded-xl p-6 border border-neon-green/30">
                  <img src="{{ url_for('static', filename='images/WX.JPG') }}"
                       alt="微信二维码"
                       class="w-32 h-32 rounded-lg shadow-lg mx-auto object-cover">
                  <p class="text-neon-green text-sm mt-4 font-medium">扫码添加微信</p>
                  <p class="text-gray-400 text-xs">快速获得技术支持</p>
                </div>
              </div>
            </div>
          </div>

          <!-- 免费使用宣传语 -->
          <div class="mt-6 bg-gradient-to-r from-neon-green/20 to-neon-blue/20 rounded-lg p-4 border border-neon-green/30">
            <div class="text-center">
              <p class="text-neon-green font-bold text-lg">
                <i class="fa fa-gift mr-2"></i>
                免费使用！永不停机
              </p>
              <p class="text-gray-300 text-sm mt-1">
                专业技术支持 · 7x24小时服务
              </p>
            </div>
          </div>
        </div>

        <!-- 联系表单 -->
        <div class="bg-dark/30 backdrop-blur-sm rounded-xl p-8 border border-primary/20">
          <h3 class="text-xl font-bold text-white mb-6">在线咨询</h3>
          <form id="consultationForm" class="space-y-4">
            <div>
              <label class="block text-gray-400 mb-2">姓名</label>
              <input type="text" id="name" name="name" required
                     class="w-full bg-dark/50 border border-primary/20 rounded-lg px-4 py-2 text-white focus:outline-none focus:border-neon-blue"
                     placeholder="请输入您的姓名">
            </div>
            <div>
              <label class="block text-gray-400 mb-2">联系方式类型</label>
              <select id="contact_type" name="contact_type"
                      class="w-full bg-dark/50 border border-primary/20 rounded-lg px-4 py-2 text-white focus:outline-none focus:border-neon-blue">
                <option value="微信">微信</option>
                <option value="电话">电话</option>
                <option value="邮箱">邮箱</option>
              </select>
            </div>
            <div>
              <label class="block text-gray-400 mb-2">联系方式</label>
              <input type="text" id="contact_value" name="contact_value" required
                     class="w-full bg-dark/50 border border-primary/20 rounded-lg px-4 py-2 text-white focus:outline-none focus:border-neon-blue"
                     placeholder="请输入您的微信号">
            </div>
            <div>
              <label class="block text-gray-400 mb-2">咨询内容</label>
              <textarea id="content" name="content" required
                        class="w-full bg-dark/50 border border-primary/20 rounded-lg px-4 py-2 text-white focus:outline-none focus:border-neon-blue h-32"
                        placeholder="请详细描述您的咨询内容，我们会尽快回复您"></textarea>
            </div>
            <button type="submit" id="submitBtn"
                    class="w-full bg-gradient-to-r from-primary to-neon-blue text-white font-medium px-8 py-3 rounded-lg transition-all shadow-lg hover:shadow-neon-blue/30">
              提交咨询
            </button>
          </form>

          <!-- 提示消息区域 -->
          <div id="messageArea" class="mt-4 hidden">
            <div id="messageContent" class="p-4 rounded-lg text-center font-medium"></div>
          </div>
        </div>
      </div>
    </div>
  </section>

  <!-- 页脚 -->
  <footer class="bg-dark-blue border-t border-primary/20 py-12">
    <div class="container mx-auto px-4 sm:px-6 lg:px-8">
      <div class="grid grid-cols-1 md:grid-cols-4 gap-8">
        <!-- 公司信息 -->
        <div class="col-span-1 md:col-span-2">
          <div class="flex items-center space-x-2 mb-4">
            <div class="w-10 h-10 bg-primary rounded-lg flex items-center justify-center">
              <i class="fa fa-cutlery text-white text-xl"></i>
            </div>
            <span class="text-xl font-bold text-white">智慧食堂<span class="text-neon-blue">平台</span></span>
          </div>
          <p class="text-gray-400 mb-4">
            致力于打造全方位智能化管理体系，实现食品安全可视化、可管控、可追溯，为校园食堂管理提供高效便捷的技术支持。
          </p>

          <!-- 免费使用宣传 -->
          <div class="bg-gradient-to-r from-neon-green/10 to-neon-blue/10 rounded-lg p-3 mb-4 border border-neon-green/20">
            <p class="text-neon-green font-bold text-lg">
              <i class="fa fa-gift mr-2"></i>
              免费使用！永不停机
            </p>
            <p class="text-gray-300 text-sm">
              专业技术支持 · 7x24小时服务 · 无限制使用
            </p>
          </div>
          <div class="flex space-x-4">
            <a href="#" class="text-gray-400 hover:text-neon-blue transition-colors">
              <i class="fa fa-weixin text-xl"></i>
            </a>
            <a href="#" class="text-gray-400 hover:text-neon-blue transition-colors">
              <i class="fa fa-weibo text-xl"></i>
            </a>
            <a href="#" class="text-gray-400 hover:text-neon-blue transition-colors">
              <i class="fa fa-qq text-xl"></i>
            </a>
          </div>
        </div>

        <!-- 快速链接 -->
        <div>
          <h4 class="text-white font-bold mb-4">快速链接</h4>
          <ul class="space-y-2">
            <li><a href="#features" class="text-gray-400 hover:text-neon-blue transition-colors">核心功能</a></li>
            <li><a href="#advantages" class="text-gray-400 hover:text-neon-blue transition-colors">系统优势</a></li>
            <li><a href="#contact" class="text-gray-400 hover:text-neon-blue transition-colors">联系我们</a></li>
          </ul>
        </div>

        <!-- 联系方式 -->
        <div>
          <h4 class="text-white font-bold mb-4">联系方式</h4>
          <ul class="space-y-2">
            <li class="text-gray-400">
              <i class="fa fa-phone mr-2"></i>
              18373062333
            </li>
            <li class="text-gray-400">
              <i class="fa fa-envelope mr-2"></i>
              <EMAIL>
            </li>
            <li class="text-gray-400">
              <i class="fa fa-map-marker mr-2"></i>
              湖南.岳阳
            </li>
          </ul>
        </div>
      </div>

      <div class="border-t border-primary/20 mt-8 pt-8 text-center">
          <p class="text-gray-400">
          © 2025 智慧食堂平台. All rights reserved.
        </p>
      </div>
    </div>
  </footer>

  <!-- 首页交互脚本 -->
  <script nonce="{{ csp_nonce }}" src="{{ url_for('static', filename='js/homepage.js') }}?v=2.0"></script>







</body>
</html>