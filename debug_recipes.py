#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
调试脚本：查看用户18373062333的食谱数据
"""

import sys
import os

# 添加项目根目录到Python路径
sys.path.insert(0, os.path.dirname(os.path.abspath(__file__)))

from app import create_app, db
from app.models import User, Recipe, AdministrativeArea
from sqlalchemy import text

def main():
    app = create_app()
    
    with app.app_context():
        print("=" * 60)
        print("查看用户18373062333的食谱数据")
        print("=" * 60)
        
        # 1. 查找用户
        user = User.query.filter_by(username='18373062333').first()
        if not user:
            print("❌ 未找到用户18373062333")
            return
            
        print(f"✅ 找到用户: {user.username}")
        print(f"   用户ID: {user.id}")
        print(f"   用户区域ID: {user.area_id}")
        
        # 2. 查找用户的区域信息
        if user.area_id:
            area = AdministrativeArea.query.get(user.area_id)
            if area:
                print(f"   用户区域: {area.name}")
            else:
                print(f"   ❌ 区域ID {user.area_id} 不存在")
        else:
            print("   ⚠️ 用户没有绑定区域")
            
        print("\n" + "=" * 60)
        print("查看所有食谱数据（按来源分类）")
        print("=" * 60)
        
        # 3. 查看所有食谱，按来源分类
        all_recipes = Recipe.query.filter_by(status=1).order_by(Recipe.id).all()
        
        school_recipes = []  # 学校食谱
        system_recipes = []  # 系统食谱
        other_recipes = []   # 其他食谱
        
        for recipe in all_recipes:
            # 判断食谱来源
            if user.area_id and recipe.area_id == user.area_id:
                school_recipes.append(recipe)
            elif recipe.is_global:
                system_recipes.append(recipe)
            elif recipe.area_id is None:
                system_recipes.append(recipe)  # 兼容旧数据
            else:
                other_recipes.append(recipe)
        
        # 4. 显示学校食谱
        print(f"\n🏫 学校食谱 (area_id={user.area_id}):")
        print("-" * 40)
        if school_recipes:
            for i, recipe in enumerate(school_recipes, 1):
                print(f"{i:2d}. ID:{recipe.id:4d} | {recipe.name}")
                print(f"     分类:{recipe.category} | area_id:{recipe.area_id} | is_global:{recipe.is_global}")
        else:
            print("   ❌ 没有学校食谱")
            
        # 5. 显示系统食谱（只显示前10个）
        print(f"\n🌐 系统食谱 (is_global=True 或 area_id=None):")
        print("-" * 40)
        if system_recipes:
            for i, recipe in enumerate(system_recipes[:10], 1):
                print(f"{i:2d}. ID:{recipe.id:4d} | {recipe.name}")
                print(f"     分类:{recipe.category} | area_id:{recipe.area_id} | is_global:{recipe.is_global}")
            if len(system_recipes) > 10:
                print(f"   ... 还有 {len(system_recipes) - 10} 个系统食谱")
        else:
            print("   ❌ 没有系统食谱")
            
        # 6. 显示其他食谱
        if other_recipes:
            print(f"\n❓ 其他食谱 (属于其他学校):")
            print("-" * 40)
            for i, recipe in enumerate(other_recipes[:5], 1):
                print(f"{i:2d}. ID:{recipe.id:4d} | {recipe.name}")
                print(f"     分类:{recipe.category} | area_id:{recipe.area_id} | is_global:{recipe.is_global}")
            if len(other_recipes) > 5:
                print(f"   ... 还有 {len(other_recipes) - 5} 个其他学校食谱")
        
        # 7. 查找"鲜椒青瓜干"相关的食谱
        print(f"\n🔍 查找包含'青瓜'的食谱:")
        print("-" * 40)
        cucumber_recipes = Recipe.query.filter(
            Recipe.name.like('%青瓜%'),
            Recipe.status == 1
        ).order_by(Recipe.id).all()
        
        if cucumber_recipes:
            for recipe in cucumber_recipes:
                source = "🏫学校" if (user.area_id and recipe.area_id == user.area_id) else "🌐系统"
                print(f"   {source} | ID:{recipe.id:4d} | {recipe.name}")
                print(f"           分类:{recipe.category} | area_id:{recipe.area_id} | is_global:{recipe.is_global}")
        else:
            print("   ❌ 没有找到包含'青瓜'的食谱")
            
        # 8. 统计信息
        print(f"\n📊 统计信息:")
        print("-" * 40)
        print(f"   学校食谱数量: {len(school_recipes)}")
        print(f"   系统食谱数量: {len(system_recipes)}")
        print(f"   其他食谱数量: {len(other_recipes)}")
        print(f"   总食谱数量: {len(all_recipes)}")

if __name__ == '__main__':
    main()
