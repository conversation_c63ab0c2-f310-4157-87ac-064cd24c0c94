/**
 * 首页专用样式文件
 * 替代外部CDN资源，提升加载速度
 */

/* 基础重置和字体 */
* {
  margin: 0;
  padding: 0;
  box-sizing: border-box;
}

body {
  font-family: 'Microsoft YaHei', 'PingFang SC', 'Helvetica Neue', Arial, sans-serif;
  line-height: 1.6;
  color: #ffffff;
  background: #0B0E2F;
  overflow-x: hidden;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
}

/* 颜色变量 */
:root {
  --primary: #165DFF;
  --secondary: #36CBCB;
  --accent: #722ED1;
  --dark: #1D2129;
  --light: #F7F8FA;
  --primary-light: #E8F3FF;
  --primary-dark: #0D47A1;
  --neon-blue: #00BFFF;
  --neon-purple: #9D4EDD;
  --neon-green: #00FF9D;
  --dark-blue: #0B0E2F;
}

/* 容器和布局 */
.container {
  max-width: 1200px;
  margin: 0 auto;
  padding: 0 1rem;
}

.section-padding {
  padding: 4rem 0 6rem;
}

/* 网格系统 */
.grid {
  display: grid;
}

.grid-cols-1 { grid-template-columns: repeat(1, minmax(0, 1fr)); }
.grid-cols-2 { grid-template-columns: repeat(2, minmax(0, 1fr)); }
.grid-cols-3 { grid-template-columns: repeat(3, minmax(0, 1fr)); }
.grid-cols-4 { grid-template-columns: repeat(4, minmax(0, 1fr)); }

.gap-4 { gap: 1rem; }
.gap-6 { gap: 1.5rem; }
.gap-8 { gap: 2rem; }

/* Flexbox */
.flex { display: flex; }
.flex-col { flex-direction: column; }
.flex-wrap { flex-wrap: wrap; }
.items-center { align-items: center; }
.items-end { align-items: flex-end; }
.justify-center { justify-content: center; }
.justify-between { justify-content: space-between; }
.space-x-2 > * + * { margin-left: 0.5rem; }
.space-x-4 > * + * { margin-left: 1rem; }
.space-x-8 > * + * { margin-left: 2rem; }
.space-y-3 > * + * { margin-top: 0.75rem; }
.space-y-4 > * + * { margin-top: 1rem; }

/* 定位 */
.relative { position: relative; }
.absolute { position: absolute; }
.fixed { position: fixed; }
.inset-0 { top: 0; right: 0; bottom: 0; left: 0; }

/* 尺寸 */
.w-full { width: 100%; }
.w-1\/2 { width: 50%; }
.w-10 { width: 2.5rem; }
.w-14 { width: 3.5rem; }
.h-10 { height: 2.5rem; }
.h-14 { height: 3.5rem; }
.h-16 { height: 4rem; }
.h-20 { height: 5rem; }
.h-48 { height: 12rem; }

/* 间距 */
.p-4 { padding: 1rem; }
.p-6 { padding: 1.5rem; }
.p-8 { padding: 2rem; }
.px-4 { padding-left: 1rem; padding-right: 1rem; }
.px-6 { padding-left: 1.5rem; padding-right: 1.5rem; }
.px-8 { padding-left: 2rem; padding-right: 2rem; }
.py-2 { padding-top: 0.5rem; padding-bottom: 0.5rem; }
.py-3 { padding-top: 0.75rem; padding-bottom: 0.75rem; }
.py-12 { padding-top: 3rem; padding-bottom: 3rem; }
.py-16 { padding-top: 4rem; padding-bottom: 4rem; }
.pt-24 { padding-top: 6rem; }
.pt-32 { padding-top: 8rem; }
.pb-16 { padding-bottom: 4rem; }
.pb-24 { padding-bottom: 6rem; }
.mb-4 { margin-bottom: 1rem; }
.mb-6 { margin-bottom: 1.5rem; }
.mb-8 { margin-bottom: 2rem; }
.mb-10 { margin-bottom: 2.5rem; }
.mb-12 { margin-bottom: 3rem; }
.mb-16 { margin-bottom: 4rem; }
.mt-3 { margin-top: 0.75rem; }
.mt-12 { margin-top: 3rem; }
.ml-2 { margin-left: 0.5rem; }

/* 文字 */
.text-xs { font-size: 0.75rem; }
.text-sm { font-size: 0.875rem; }
.text-lg { font-size: 1.125rem; }
.text-xl { font-size: 1.25rem; }
.text-2xl { font-size: 1.5rem; }
.text-4xl { font-size: 2.25rem; }
.font-medium { font-weight: 500; }
.font-bold { font-weight: 700; }
.text-center { text-align: center; }
.text-white { color: #ffffff; }
.text-gray-300 { color: #d1d5db; }
.text-gray-400 { color: #9ca3af; }
.text-gray-500 { color: #6b7280; }
.leading-tight { line-height: 1.25; }

/* 背景 */
.bg-transparent { background-color: transparent; }
.bg-dark-blue { background-color: var(--dark-blue); }
.bg-primary { background-color: var(--primary); }

/* 边框 */
.border { border-width: 1px; }
.border-primary\/20 { border-color: rgba(22, 93, 255, 0.2); }
.border-neon-blue\/50 { border-color: rgba(0, 191, 255, 0.5); }
.border-neon-green\/50 { border-color: rgba(0, 255, 157, 0.5); }
.rounded-lg { border-radius: 0.5rem; }
.rounded-xl { border-radius: 0.75rem; }
.rounded-2xl { border-radius: 1rem; }
.rounded-full { border-radius: 9999px; }

/* 阴影 */
.shadow-lg { box-shadow: 0 10px 15px -3px rgba(0, 0, 0, 0.1), 0 4px 6px -2px rgba(0, 0, 0, 0.05); }
.shadow-2xl { box-shadow: 0 25px 50px -12px rgba(0, 0, 0, 0.25); }

/* 过渡动画 */
.transition-all { transition: all 0.3s ease; }
.transition-colors { transition: color 0.3s ease, background-color 0.3s ease; }
.transition-transform { transition: transform 0.3s ease; }

/* 特殊效果 */
.backdrop-blur-sm { backdrop-filter: blur(4px); }
.backdrop-blur-md { backdrop-filter: blur(12px); }

.neon-glow {
  box-shadow: 0 0 10px rgba(0, 191, 255, 0.5), 0 0 20px rgba(0, 191, 255, 0.3);
}

.neon-text {
  text-shadow: 0 0 10px rgba(0, 191, 255, 0.7);
}

.text-neon-blue { color: var(--neon-blue); }
.text-neon-green { color: var(--neon-green); }
.text-neon-purple { color: var(--neon-purple); }

/* 动画 */
@keyframes float {
  0% { transform: translateY(0px); }
  50% { transform: translateY(-20px); }
  100% { transform: translateY(0px); }
}

@keyframes pulse {
  0% { transform: scale(1); opacity: 1; }
  50% { transform: scale(1.05); opacity: 0.8; }
  100% { transform: scale(1); opacity: 1; }
}

.animate-float {
  animation: float 6s ease-in-out infinite;
}

.data-pulse {
  animation: pulse 2s infinite;
}

/* 背景网格 */
.bg-grid {
  background-image: 
    linear-gradient(rgba(22, 93, 255, 0.05) 1px, transparent 1px),
    linear-gradient(90deg, rgba(22, 93, 255, 0.05) 1px, transparent 1px);
  background-size: 20px 20px;
}

/* 渐变背景 */
.bg-gradient-primary {
  background: linear-gradient(to right, var(--primary), var(--primary-dark));
}

.bg-gradient-to-r {
  background: linear-gradient(to right, var(--primary), var(--neon-blue));
}

.bg-gradient-to-b {
  background: linear-gradient(to bottom, rgba(22, 93, 255, 0.05), var(--dark-blue));
}

/* 卡片悬停效果 */
.card-hover {
  transition: all 0.3s ease;
}

.card-hover:hover {
  transform: translateY(-4px);
  box-shadow: 0 20px 25px -5px rgba(0, 0, 0, 0.1), 0 10px 10px -5px rgba(0, 0, 0, 0.04);
}

/* 按钮样式 */
.btn {
  display: inline-flex;
  align-items: center;
  justify-content: center;
  padding: 0.75rem 2rem;
  border-radius: 0.5rem;
  font-weight: 500;
  text-decoration: none;
  transition: all 0.3s ease;
  cursor: pointer;
  border: none;
}

.btn-primary {
  background: linear-gradient(to right, var(--primary), var(--neon-blue));
  color: white;
  box-shadow: 0 0 10px rgba(0, 191, 255, 0.5);
}

.btn-primary:hover {
  box-shadow: 0 0 20px rgba(0, 191, 255, 0.7);
  transform: translateY(-2px);
}

.btn-outline {
  background: transparent;
  color: white;
  border: 1px solid rgba(0, 191, 255, 0.5);
}

.btn-outline:hover {
  background: rgba(255, 255, 255, 0.1);
  border-color: var(--neon-blue);
}

/* 导航栏 */
.navbar {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  z-index: 50;
  background: rgba(11, 14, 47, 0.8);
  backdrop-filter: blur(12px);
  border-bottom: 1px solid rgba(22, 93, 255, 0.2);
  transition: all 0.3s ease;
}

.navbar-brand {
  display: flex;
  align-items: center;
  text-decoration: none;
  color: white;
  font-size: 1.25rem;
  font-weight: 700;
}

.navbar-nav {
  display: flex;
  list-style: none;
  margin: 0;
  padding: 0;
}

.nav-link {
  color: white;
  text-decoration: none;
  padding: 0.5rem 1rem;
  transition: color 0.3s ease;
}

.nav-link:hover {
  color: var(--neon-blue);
}

/* 响应式设计 */
@media (max-width: 768px) {
  .container {
    padding: 0 1rem;
  }
  
  .grid-cols-2 {
    grid-template-columns: repeat(1, minmax(0, 1fr));
  }
  
  .grid-cols-3 {
    grid-template-columns: repeat(1, minmax(0, 1fr));
  }
  
  .grid-cols-4 {
    grid-template-columns: repeat(2, minmax(0, 1fr));
  }
  
  .flex-col {
    flex-direction: column;
  }
  
  .text-4xl {
    font-size: 2rem;
  }
  
  .pt-32 {
    padding-top: 6rem;
  }
  
  .section-padding {
    padding: 3rem 0 4rem;
  }
}

@media (max-width: 480px) {
  .grid-cols-4 {
    grid-template-columns: repeat(1, minmax(0, 1fr));
  }
  
  .px-8 {
    padding-left: 1rem;
    padding-right: 1rem;
  }
}

/* 轮播图样式 */
.carousel-container {
  position: relative;
  width: 100%;
  height: 480px;
  border-radius: 1rem;
  overflow: hidden;
  background: rgba(0, 0, 0, 0.1);
}

.carousel-inner {
  position: relative;
  width: 100%;
  height: 100%;
}

.carousel-item {
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  opacity: 0;
  transition: opacity 0.6s ease-in-out;
}

.carousel-item.active {
  opacity: 1;
}

.carousel-item img {
  width: 100%;
  height: 100%;
  object-fit: cover;
  border-radius: 1rem;
}

.carousel-caption {
  position: absolute;
  bottom: 20px;
  left: 20px;
  right: 20px;
  background: rgba(0, 0, 0, 0.7);
  color: white;
  padding: 1rem;
  border-radius: 0.5rem;
  backdrop-filter: blur(4px);
}

.carousel-caption h5 {
  margin: 0 0 0.5rem 0;
  font-size: 1.25rem;
  font-weight: 600;
}

.carousel-caption p {
  margin: 0;
  font-size: 0.875rem;
  opacity: 0.9;
}

/* 轮播指示器 */
.carousel-indicators {
  position: absolute;
  bottom: 20px;
  left: 50%;
  transform: translateX(-50%);
  display: flex;
  gap: 8px;
  z-index: 10;
}

.carousel-indicator {
  width: 12px;
  height: 12px;
  border-radius: 50%;
  border: 2px solid rgba(255, 255, 255, 0.8);
  background: rgba(255, 255, 255, 0.5);
  cursor: pointer;
  transition: all 0.3s ease;
  opacity: 0.7;
}

.carousel-indicator.active {
  background: var(--neon-blue);
  border-color: var(--neon-blue);
  transform: scale(1.2);
  opacity: 1;
}

/* 轮播控制按钮 */
.carousel-control {
  position: absolute;
  top: 50%;
  transform: translateY(-50%);
  width: 45px;
  height: 45px;
  background: rgba(0, 0, 0, 0.4);
  border: none;
  border-radius: 50%;
  color: white;
  cursor: pointer;
  transition: all 0.3s ease;
  opacity: 0.8;
  z-index: 10;
}

.carousel-control:hover {
  background: rgba(0, 0, 0, 0.7);
  transform: translateY(-50%) scale(1.1);
  opacity: 1;
}

.carousel-control-prev {
  left: 15px;
}

.carousel-control-next {
  right: 15px;
}

.carousel-control-icon {
  width: 18px;
  height: 18px;
  display: inline-block;
}

.carousel-control-prev-icon {
  background-image: url("data:image/svg+xml,%3csvg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 16 16' fill='%23fff'%3e%3cpath d='m11.354 1.646a.5.5 0 0 1 0 .708L5.707 8l5.647 5.646a.5.5 0 0 1-.708.708l-6-6a.5.5 0 0 1 0-.708l6-6a.5.5 0 0 1 .708 0z'/%3e%3c/svg%3e");
}

.carousel-control-next-icon {
  background-image: url("data:image/svg+xml,%3csvg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 16 16' fill='%23fff'%3e%3cpath d='m4.646 1.646a.5.5 0 0 1 .708 0l6 6a.5.5 0 0 1 0 .708l-6 6a.5.5 0 0 1-.708-.708L10.293 8 4.646 2.354a.5.5 0 0 1 0-.708z'/%3e%3c/svg%3e");
}

/* 加载和空状态 */
.carousel-loading,
.carousel-empty {
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  background: rgba(0, 0, 0, 0.1);
  border-radius: 1rem;
  color: white;
}

.spinner {
  width: 3rem;
  height: 3rem;
  border: 0.3em solid rgba(255, 255, 255, 0.3);
  border-top: 0.3em solid var(--primary);
  border-radius: 50%;
  animation: spin 1s linear infinite;
  margin-bottom: 1rem;
}

@keyframes spin {
  0% { transform: rotate(0deg); }
  100% { transform: rotate(360deg); }
}

.empty-icon {
  font-size: 4rem;
  margin-bottom: 1rem;
  opacity: 0.5;
}

/* 隐藏类 */
.hidden {
  display: none;
}

.md\:hidden {
  display: block;
}

@media (min-width: 768px) {
  .md\:hidden {
    display: none;
  }

  .md\:flex {
    display: flex;
  }

  .md\:grid-cols-2 {
    grid-template-columns: repeat(2, minmax(0, 1fr));
  }

  .md\:grid-cols-3 {
    grid-template-columns: repeat(3, minmax(0, 1fr));
  }

  /* 轮播图响应式 */
  .carousel-container {
    height: 480px;
  }
}

@media (max-width: 768px) {
  .carousel-container {
    height: 360px;
    border-radius: 1rem;
  }

  .carousel-control {
    width: 35px;
    height: 35px;
  }

  .carousel-control-prev {
    left: 10px;
  }

  .carousel-control-next {
    right: 10px;
  }

  .carousel-indicator {
    width: 10px;
    height: 10px;
  }

  .carousel-caption {
    bottom: 10px;
    left: 10px;
    right: 10px;
    padding: 0.75rem;
  }

  .carousel-caption h5 {
    font-size: 1rem;
  }

  .carousel-caption p {
    font-size: 0.75rem;
  }
}

@media (min-width: 1024px) {
  .lg\:w-1\/2 {
    width: 50%;
  }

  .lg\:flex-row {
    flex-direction: row;
  }

  .lg\:grid-cols-4 {
    grid-template-columns: repeat(4, minmax(0, 1fr));
  }
}
