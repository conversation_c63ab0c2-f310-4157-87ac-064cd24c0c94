2025-06-01 17:24:01,703 INFO: 应用启动 [in C:\Users\<USER>\Documents\augment-projects\StudentsCMSSP\app\__init__.py:778]
2025-06-01 17:24:03,197 INFO: 应用启动 [in C:\Users\<USER>\Documents\augment-projects\StudentsCMSSP\app\__init__.py:778]
2025-06-01 17:24:40,888 INFO: 开始查询最近 5 条陪餐记录 [in C:\Users\<USER>\Documents\augment-projects\StudentsCMSSP\app\routes\dashboard_api.py:57]
2025-06-01 17:24:40,915 INFO: 成功获取 0 条陪餐记录 [in C:\Users\<USER>\Documents\augment-projects\StudentsCMSSP\app\routes\dashboard_api.py:91]
2025-06-01 17:24:46,885 INFO: 开始查询最近 5 条陪餐记录 [in C:\Users\<USER>\Documents\augment-projects\StudentsCMSSP\app\routes\dashboard_api.py:57]
2025-06-01 17:24:46,894 INFO: 成功获取 0 条陪餐记录 [in C:\Users\<USER>\Documents\augment-projects\StudentsCMSSP\app\routes\dashboard_api.py:91]
2025-06-01 17:24:49,971 INFO: 开始查询最近 5 条陪餐记录 [in C:\Users\<USER>\Documents\augment-projects\StudentsCMSSP\app\routes\dashboard_api.py:57]
2025-06-01 17:24:49,980 INFO: 成功获取 0 条陪餐记录 [in C:\Users\<USER>\Documents\augment-projects\StudentsCMSSP\app\routes\dashboard_api.py:91]
2025-06-01 17:24:57,165 WARNING: 无法解析日期时间字符串:  [in C:\Users\<USER>\Documents\augment-projects\StudentsCMSSP\app\utils\datetime_helper.py:297]
2025-06-01 17:25:02,000 INFO: 生成固定二维码 - 用户: 18373062333, 学校: 朝阳区实验中学 (ID: 42) [in C:\Users\<USER>\Documents\augment-projects\StudentsCMSSP\app\routes\daily_management\inspection_qrcode.py:31]
2025-06-01 17:25:02,001 INFO: 员工上传URL: http://192.168.1.7:5000/daily-management/public/inspections/select-date/42/upload [in C:\Users\<USER>\Documents\augment-projects\StudentsCMSSP\app\routes\daily_management\inspection_qrcode.py:38]
2025-06-01 17:25:02,019 INFO: 成功生成二维码base64，数据长度: 1056 [in C:\Users\<USER>\Documents\augment-projects\StudentsCMSSP\app\utils\qrcode_helper.py:78]
2025-06-01 17:25:02,020 INFO: 管理员评分URL: http://192.168.1.7:5000/daily-management/public/inspections/select-date/42/rate [in C:\Users\<USER>\Documents\augment-projects\StudentsCMSSP\app\routes\daily_management\inspection_qrcode.py:46]
2025-06-01 17:25:02,035 INFO: 成功生成二维码base64，数据长度: 1092 [in C:\Users\<USER>\Documents\augment-projects\StudentsCMSSP\app\utils\qrcode_helper.py:78]
2025-06-01 17:25:44,105 WARNING: 无法解析日期时间字符串:  [in C:\Users\<USER>\Documents\augment-projects\StudentsCMSSP\app\utils\datetime_helper.py:297]
2025-06-01 17:26:23,345 WARNING: 无法解析日期时间字符串:  [in C:\Users\<USER>\Documents\augment-projects\StudentsCMSSP\app\utils\datetime_helper.py:297]
2025-06-01 17:26:25,380 INFO: 生成固定二维码 - 用户: 18373062333, 学校: 朝阳区实验中学 (ID: 42) [in C:\Users\<USER>\Documents\augment-projects\StudentsCMSSP\app\routes\daily_management\inspection_qrcode.py:31]
2025-06-01 17:26:25,381 INFO: 员工上传URL: http://192.168.1.7:5000/daily-management/public/inspections/select-date/42/upload [in C:\Users\<USER>\Documents\augment-projects\StudentsCMSSP\app\routes\daily_management\inspection_qrcode.py:38]
2025-06-01 17:26:25,396 INFO: 成功生成二维码base64，数据长度: 1056 [in C:\Users\<USER>\Documents\augment-projects\StudentsCMSSP\app\utils\qrcode_helper.py:78]
2025-06-01 17:26:25,396 INFO: 管理员评分URL: http://192.168.1.7:5000/daily-management/public/inspections/select-date/42/rate [in C:\Users\<USER>\Documents\augment-projects\StudentsCMSSP\app\routes\daily_management\inspection_qrcode.py:46]
2025-06-01 17:26:25,406 INFO: 成功生成二维码base64，数据长度: 1092 [in C:\Users\<USER>\Documents\augment-projects\StudentsCMSSP\app\utils\qrcode_helper.py:78]
2025-06-01 17:26:31,548 INFO: 找到 1 条检查记录，日志ID: 53, 检查类型: noon [in C:\Users\<USER>\Documents\augment-projects\StudentsCMSSP\app\routes\daily_management\inspection_qrcode.py:270]
2025-06-01 17:26:31,548 INFO: 检查记录: ID=47, 项目=午检检查 [in C:\Users\<USER>\Documents\augment-projects\StudentsCMSSP\app\routes\daily_management\inspection_qrcode.py:272]
2025-06-01 17:26:31,553 INFO: 检查记录 47 (午检检查) 查询到 0 张照片，有效照片 0 张 [in C:\Users\<USER>\Documents\augment-projects\StudentsCMSSP\app\routes\daily_management\inspection_qrcode.py:356]
2025-06-01 17:26:31,554 INFO: 检查记录 午检检查 没有有效照片，跳过 [in C:\Users\<USER>\Documents\augment-projects\StudentsCMSSP\app\routes\daily_management\inspection_qrcode.py:362]
2025-06-01 17:26:31,554 INFO: 最终photos_by_item包含 0 个项目: [] [in C:\Users\<USER>\Documents\augment-projects\StudentsCMSSP\app\routes\daily_management\inspection_qrcode.py:372]
2025-06-01 17:29:43,764 INFO: 应用启动 [in C:\Users\<USER>\Documents\augment-projects\StudentsCMSSP\app\__init__.py:778]
2025-06-01 17:30:19,177 INFO: 应用启动 [in C:\Users\<USER>\Documents\augment-projects\StudentsCMSSP\app\__init__.py:778]
2025-06-01 17:31:10,251 INFO: 找到 1 条检查记录，日志ID: 53, 检查类型: noon [in C:\Users\<USER>\Documents\augment-projects\StudentsCMSSP\app\routes\daily_management\inspection_qrcode.py:270]
2025-06-01 17:31:10,252 INFO: 检查记录: ID=47, 项目=午检检查 [in C:\Users\<USER>\Documents\augment-projects\StudentsCMSSP\app\routes\daily_management\inspection_qrcode.py:272]
2025-06-01 17:31:41,796 INFO: 应用启动 [in C:\Users\<USER>\Documents\augment-projects\StudentsCMSSP\app\__init__.py:778]
