2025-06-01 02:48:37,804 INFO: 应用启动 [in C:\Users\<USER>\Documents\augment-projects\StudentsCMSSP\app\__init__.py:778]
2025-06-01 02:48:39,333 INFO: 应用启动 [in C:\Users\<USER>\Documents\augment-projects\StudentsCMSSP\app\__init__.py:778]
2025-06-01 02:49:10,300 INFO: 开始查询最近 5 条陪餐记录 [in C:\Users\<USER>\Documents\augment-projects\StudentsCMSSP\app\routes\dashboard_api.py:57]
2025-06-01 02:49:10,318 INFO: 成功获取 0 条陪餐记录 [in C:\Users\<USER>\Documents\augment-projects\StudentsCMSSP\app\routes\dashboard_api.py:91]
2025-06-01 02:49:24,239 INFO: 开始查询最近 5 条陪餐记录 [in C:\Users\<USER>\Documents\augment-projects\StudentsCMSSP\app\routes\dashboard_api.py:57]
2025-06-01 02:49:24,246 INFO: 成功获取 0 条陪餐记录 [in C:\Users\<USER>\Documents\augment-projects\StudentsCMSSP\app\routes\dashboard_api.py:91]
2025-06-01 14:56:46,248 INFO: 应用启动 [in C:\Users\<USER>\Documents\augment-projects\StudentsCMSSP\app\__init__.py:778]
2025-06-01 14:56:47,574 INFO: 应用启动 [in C:\Users\<USER>\Documents\augment-projects\StudentsCMSSP\app\__init__.py:778]
2025-06-01 14:56:55,330 INFO: 开始查询最近 5 条陪餐记录 [in C:\Users\<USER>\Documents\augment-projects\StudentsCMSSP\app\routes\dashboard_api.py:57]
2025-06-01 14:56:55,349 INFO: 成功获取 0 条陪餐记录 [in C:\Users\<USER>\Documents\augment-projects\StudentsCMSSP\app\routes\dashboard_api.py:91]
2025-06-01 14:56:59,841 INFO: 获取周菜单: area_id=42, week_start=2025-05-26, 类型=<class 'datetime.date'> [in C:\Users\<USER>\Documents\augment-projects\StudentsCMSSP\app\services\weekly_menu_service.py:33]
2025-06-01 14:56:59,841 INFO: 使用日期字符串: 2025-05-26 [in C:\Users\<USER>\Documents\augment-projects\StudentsCMSSP\app\services\weekly_menu_service.py:51]
2025-06-01 14:56:59,841 INFO: 执行SQL: 
                SELECT TOP 1 id, area_id, week_start, week_end, status, created_by, created_at, updated_at
                FROM weekly_menus
                WHERE area_id = :area_id AND CONVERT(VARCHAR(10), week_start, 120) = :week_start_str
                 [in C:\Users\<USER>\Documents\augment-projects\StudentsCMSSP\app\services\weekly_menu_service.py:63]
2025-06-01 14:56:59,841 INFO: SQL参数: area_id=42, week_start_str=2025-05-26 [in C:\Users\<USER>\Documents\augment-projects\StudentsCMSSP\app\services\weekly_menu_service.py:64]
2025-06-01 14:56:59,843 INFO: SQL查询成功，找到菜单: id=36 [in C:\Users\<USER>\Documents\augment-projects\StudentsCMSSP\app\services\weekly_menu_service.py:70]
2025-06-01 14:56:59,845 INFO: 通过ID获取完整菜单对象成功: id=36 [in C:\Users\<USER>\Documents\augment-projects\StudentsCMSSP\app\services\weekly_menu_service.py:75]
2025-06-01 14:56:59,845 INFO: 获取周菜单: area_id=42, week_start=2025-05-26, 类型=<class 'datetime.date'> [in C:\Users\<USER>\Documents\augment-projects\StudentsCMSSP\app\services\weekly_menu_service.py:33]
2025-06-01 14:56:59,846 INFO: 使用日期字符串: 2025-05-26 [in C:\Users\<USER>\Documents\augment-projects\StudentsCMSSP\app\services\weekly_menu_service.py:51]
2025-06-01 14:56:59,846 INFO: 执行SQL: 
                SELECT TOP 1 id, area_id, week_start, week_end, status, created_by, created_at, updated_at
                FROM weekly_menus
                WHERE area_id = :area_id AND CONVERT(VARCHAR(10), week_start, 120) = :week_start_str
                 [in C:\Users\<USER>\Documents\augment-projects\StudentsCMSSP\app\services\weekly_menu_service.py:63]
2025-06-01 14:56:59,846 INFO: SQL参数: area_id=42, week_start_str=2025-05-26 [in C:\Users\<USER>\Documents\augment-projects\StudentsCMSSP\app\services\weekly_menu_service.py:64]
2025-06-01 14:56:59,846 INFO: SQL查询成功，找到菜单: id=36 [in C:\Users\<USER>\Documents\augment-projects\StudentsCMSSP\app\services\weekly_menu_service.py:70]
2025-06-01 14:56:59,847 INFO: 通过ID获取完整菜单对象成功: id=36 [in C:\Users\<USER>\Documents\augment-projects\StudentsCMSSP\app\services\weekly_menu_service.py:75]
2025-06-01 14:56:59,847 INFO: 获取周菜单: area_id=42, week_start=2025-05-19, 类型=<class 'datetime.date'> [in C:\Users\<USER>\Documents\augment-projects\StudentsCMSSP\app\services\weekly_menu_service.py:33]
2025-06-01 14:56:59,847 INFO: 使用日期字符串: 2025-05-19 [in C:\Users\<USER>\Documents\augment-projects\StudentsCMSSP\app\services\weekly_menu_service.py:51]
2025-06-01 14:56:59,847 INFO: 执行SQL: 
                SELECT TOP 1 id, area_id, week_start, week_end, status, created_by, created_at, updated_at
                FROM weekly_menus
                WHERE area_id = :area_id AND CONVERT(VARCHAR(10), week_start, 120) = :week_start_str
                 [in C:\Users\<USER>\Documents\augment-projects\StudentsCMSSP\app\services\weekly_menu_service.py:63]
2025-06-01 14:56:59,848 INFO: SQL参数: area_id=42, week_start_str=2025-05-19 [in C:\Users\<USER>\Documents\augment-projects\StudentsCMSSP\app\services\weekly_menu_service.py:64]
2025-06-01 14:56:59,849 INFO: SQL查询未找到菜单: area_id=42, week_start_str=2025-05-19 [in C:\Users\<USER>\Documents\augment-projects\StudentsCMSSP\app\services\weekly_menu_service.py:95]
2025-06-01 14:56:59,850 INFO: 获取周菜单: area_id=42, week_start=2025-06-02, 类型=<class 'datetime.date'> [in C:\Users\<USER>\Documents\augment-projects\StudentsCMSSP\app\services\weekly_menu_service.py:33]
2025-06-01 14:56:59,850 INFO: 使用日期字符串: 2025-06-02 [in C:\Users\<USER>\Documents\augment-projects\StudentsCMSSP\app\services\weekly_menu_service.py:51]
2025-06-01 14:56:59,851 INFO: 执行SQL: 
                SELECT TOP 1 id, area_id, week_start, week_end, status, created_by, created_at, updated_at
                FROM weekly_menus
                WHERE area_id = :area_id AND CONVERT(VARCHAR(10), week_start, 120) = :week_start_str
                 [in C:\Users\<USER>\Documents\augment-projects\StudentsCMSSP\app\services\weekly_menu_service.py:63]
2025-06-01 14:56:59,851 INFO: SQL参数: area_id=42, week_start_str=2025-06-02 [in C:\Users\<USER>\Documents\augment-projects\StudentsCMSSP\app\services\weekly_menu_service.py:64]
2025-06-01 14:56:59,853 INFO: SQL查询成功，找到菜单: id=37 [in C:\Users\<USER>\Documents\augment-projects\StudentsCMSSP\app\services\weekly_menu_service.py:70]
2025-06-01 14:56:59,855 INFO: 通过ID获取完整菜单对象成功: id=37 [in C:\Users\<USER>\Documents\augment-projects\StudentsCMSSP\app\services\weekly_menu_service.py:75]
2025-06-01 15:10:50,499 INFO: 复制食谱 - 原食谱ID: 366, 名称: 黑木耳炒山药 [in C:\Users\<USER>\Documents\augment-projects\StudentsCMSSP\app\routes\recipe.py:807]
2025-06-01 15:10:50,499 INFO: 原食谱属性 - is_global: True, area_id: None [in C:\Users\<USER>\Documents\augment-projects\StudentsCMSSP\app\routes\recipe.py:808]
2025-06-01 15:10:50,513 INFO: 用户区域信息 - user_area: 朝阳区实验中学, ID: 42 [in C:\Users\<USER>\Documents\augment-projects\StudentsCMSSP\app\routes\recipe.py:812]
2025-06-01 15:10:54,837 INFO: 复制食谱 - 原食谱ID: 365, 名称: 黄米南瓜盅 [in C:\Users\<USER>\Documents\augment-projects\StudentsCMSSP\app\routes\recipe.py:807]
2025-06-01 15:10:54,838 INFO: 原食谱属性 - is_global: True, area_id: None [in C:\Users\<USER>\Documents\augment-projects\StudentsCMSSP\app\routes\recipe.py:808]
2025-06-01 15:10:54,838 INFO: 用户区域信息 - user_area: 朝阳区实验中学, ID: 42 [in C:\Users\<USER>\Documents\augment-projects\StudentsCMSSP\app\routes\recipe.py:812]
2025-06-01 15:11:02,670 INFO: 复制食谱 - 原食谱ID: 363, 名称: 鲜蚕豆烧大雁 [in C:\Users\<USER>\Documents\augment-projects\StudentsCMSSP\app\routes\recipe.py:807]
2025-06-01 15:11:02,670 INFO: 原食谱属性 - is_global: True, area_id: None [in C:\Users\<USER>\Documents\augment-projects\StudentsCMSSP\app\routes\recipe.py:808]
2025-06-01 15:11:02,671 INFO: 用户区域信息 - user_area: 朝阳区实验中学, ID: 42 [in C:\Users\<USER>\Documents\augment-projects\StudentsCMSSP\app\routes\recipe.py:812]
2025-06-01 15:11:04,186 INFO: 复制食谱 - 原食谱ID: 362, 名称: 鲜笋烧仔排 [in C:\Users\<USER>\Documents\augment-projects\StudentsCMSSP\app\routes\recipe.py:807]
2025-06-01 15:11:04,186 INFO: 原食谱属性 - is_global: True, area_id: None [in C:\Users\<USER>\Documents\augment-projects\StudentsCMSSP\app\routes\recipe.py:808]
2025-06-01 15:11:04,187 INFO: 用户区域信息 - user_area: 朝阳区实验中学, ID: 42 [in C:\Users\<USER>\Documents\augment-projects\StudentsCMSSP\app\routes\recipe.py:812]
2025-06-01 15:11:07,667 INFO: 复制食谱 - 原食谱ID: 362, 名称: 鲜笋烧仔排 [in C:\Users\<USER>\Documents\augment-projects\StudentsCMSSP\app\routes\recipe.py:807]
2025-06-01 15:11:07,668 INFO: 原食谱属性 - is_global: True, area_id: None [in C:\Users\<USER>\Documents\augment-projects\StudentsCMSSP\app\routes\recipe.py:808]
2025-06-01 15:11:07,669 INFO: 用户区域信息 - user_area: 朝阳区实验中学, ID: 42 [in C:\Users\<USER>\Documents\augment-projects\StudentsCMSSP\app\routes\recipe.py:812]
2025-06-01 15:11:09,175 INFO: 复制食谱 - 原食谱ID: 361, 名称: 鲜椒青瓜干 [in C:\Users\<USER>\Documents\augment-projects\StudentsCMSSP\app\routes\recipe.py:807]
2025-06-01 15:11:09,175 INFO: 原食谱属性 - is_global: True, area_id: None [in C:\Users\<USER>\Documents\augment-projects\StudentsCMSSP\app\routes\recipe.py:808]
2025-06-01 15:11:09,177 INFO: 用户区域信息 - user_area: 朝阳区实验中学, ID: 42 [in C:\Users\<USER>\Documents\augment-projects\StudentsCMSSP\app\routes\recipe.py:812]
2025-06-01 15:11:30,808 INFO: 使用日期字符串: 2025-05-26 [in C:\Users\<USER>\Documents\augment-projects\StudentsCMSSP\app\services\weekly_menu_service.py:51]
